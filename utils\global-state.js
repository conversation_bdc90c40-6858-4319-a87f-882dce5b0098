/**
 * 全局状态管理器
 * 统一管理用户状态、出生信息、登录状态等全局数据
 */

class GlobalStateManager {
  constructor() {
    this.app = getApp()
    this.listeners = new Map() // 状态变化监听器
    this.state = {
      userInfo: null,
      birthInfo: null,
      isLoggedIn: false,
      points: 0,
      signInStatus: false
    }
    
    this.init()
  }

  /**
   * 初始化状态
   */
  init() {
    this.loadFromStorage()
    this.syncWithApp()
  }

  /**
   * 从本地存储加载状态
   */
  loadFromStorage() {
    try {
      this.state.userInfo = wx.getStorageSync('user_info') || null
      this.state.birthInfo = wx.getStorageSync('birthInfo') || null
      this.state.points = wx.getStorageSync('user_points') || 0
      this.state.isLoggedIn = !!(wx.getStorageSync('access_token') && this.state.userInfo)
      
      // 检查今日签到状态
      const today = new Date().toLocaleDateString()
      const lastSignIn = wx.getStorageSync('lastSignIn')
      this.state.signInStatus = today === lastSignIn
      
    } catch (error) {
      console.error('加载本地状态失败:', error)
    }
  }

  /**
   * 与App全局数据同步
   */
  syncWithApp() {
    if (this.app && this.app.globalData) {
      // 如果App中有用户信息，优先使用
      if (this.app.globalData.userInfo) {
        this.state.userInfo = this.app.globalData.userInfo
      }
    }
  }

  /**
   * 获取状态
   * @param {string} key - 状态键名
   * @returns {any} 状态值
   */
  getState(key) {
    if (key) {
      return this.state[key]
    }
    return { ...this.state }
  }

  /**
   * 设置状态
   * @param {string|Object} key - 状态键名或状态对象
   * @param {any} value - 状态值
   */
  setState(key, value) {
    const oldState = { ...this.state }
    
    if (typeof key === 'object') {
      // 批量设置
      Object.assign(this.state, key)
    } else {
      // 单个设置
      this.state[key] = value
    }
    
    // 同步到本地存储
    this.syncToStorage(key, value)
    
    // 同步到App
    this.syncToApp()
    
    // 通知监听器
    this.notifyListeners(oldState, this.state)
  }

  /**
   * 同步到本地存储
   * @param {string|Object} key - 状态键名或状态对象
   * @param {any} value - 状态值
   */
  syncToStorage(key, value) {
    try {
      if (typeof key === 'object') {
        // 批量同步
        Object.entries(key).forEach(([k, v]) => {
          this.syncSingleToStorage(k, v)
        })
      } else {
        this.syncSingleToStorage(key, value)
      }
    } catch (error) {
      console.error('同步到本地存储失败:', error)
    }
  }

  /**
   * 同步单个状态到本地存储
   * @param {string} key - 状态键名
   * @param {any} value - 状态值
   */
  syncSingleToStorage(key, value) {
    const storageMap = {
      userInfo: 'user_info',
      birthInfo: 'birthInfo',
      points: 'user_points',
      isLoggedIn: 'is_logged_in'
    }
    
    const storageKey = storageMap[key]
    if (storageKey && value !== null && value !== undefined) {
      wx.setStorageSync(storageKey, value)
    }
  }

  /**
   * 同步到App全局数据
   */
  syncToApp() {
    if (this.app && this.app.globalData) {
      this.app.globalData.userInfo = this.state.userInfo
    }
  }

  /**
   * 添加状态变化监听器
   * @param {string} key - 监听的状态键名
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听的函数
   */
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set())
    }
    
    this.listeners.get(key).add(callback)
    
    // 返回取消监听的函数
    return () => {
      const keyListeners = this.listeners.get(key)
      if (keyListeners) {
        keyListeners.delete(callback)
      }
    }
  }

  /**
   * 通知监听器
   * @param {Object} oldState - 旧状态
   * @param {Object} newState - 新状态
   */
  notifyListeners(oldState, newState) {
    Object.keys(newState).forEach(key => {
      if (oldState[key] !== newState[key]) {
        const keyListeners = this.listeners.get(key)
        if (keyListeners) {
          keyListeners.forEach(callback => {
            try {
              callback(newState[key], oldState[key])
            } catch (error) {
              console.error(`状态监听器执行失败 (${key}):`, error)
            }
          })
        }
      }
    })
  }

  /**
   * 更新用户信息
   * @param {Object} userInfo - 用户信息
   */
  updateUserInfo(userInfo) {
    this.setState('userInfo', userInfo)
    this.setState('isLoggedIn', !!userInfo)
  }

  /**
   * 更新出生信息
   * @param {Object} birthInfo - 出生信息
   */
  updateBirthInfo(birthInfo) {
    this.setState('birthInfo', birthInfo)
  }

  /**
   * 更新积分
   * @param {number} points - 积分数量
   */
  updatePoints(points) {
    this.setState('points', points)
  }

  /**
   * 更新签到状态
   * @param {boolean} status - 签到状态
   */
  updateSignInStatus(status) {
    this.setState('signInStatus', status)
    if (status) {
      wx.setStorageSync('lastSignIn', new Date().toLocaleDateString())
    }
  }

  /**
   * 登出
   */
  logout() {
    this.setState({
      userInfo: null,
      isLoggedIn: false
    })
    
    // 清除相关存储
    wx.removeStorageSync('user_info')
    wx.removeStorageSync('access_token')
    wx.removeStorageSync('refresh_token')
  }

  /**
   * 检查是否有出生信息
   * @returns {boolean} 是否有出生信息
   */
  hasBirthInfo() {
    const birthInfo = this.state.birthInfo
    return !!(birthInfo && birthInfo.name && birthInfo.selectedDateTime)
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isUserLoggedIn() {
    return this.state.isLoggedIn
  }

  // 企业微信环境检查方法已移除
}

// 创建单例实例
const globalState = new GlobalStateManager()

module.exports = globalState

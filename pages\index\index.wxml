<!--index.wxml-->
<view class="page-container">
  <scroll-view class="scrollarea" scroll-y type="list">
    <view class="container">
      <!-- 加载中 -->
      <view class="loading" wx:if="{{isLoading}}">
        <image class="loading-icon" src="data:image/gif;base64,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" />
      </view>

      <!-- 主要内容 -->
      <view class="content" wx:else>
      <!-- 新增：时间问候头部 -->
      <view class="time-greeting-header">
        <view class="greeting-section">
          <text class="greeting-text">{{greeting}}</text>
          <text class="user-name" wx:if="{{userInfo.nickName}}">{{userInfo.nickName}}</text>
        </view>
        
        <view class="calendar-section">
          <view class="solar-date">
            <text class="date-number">{{todayInfo.solar.day}}</text>
            <view class="date-info">
              <text class="month-year">{{todayInfo.solar.year}}年{{todayInfo.solar.month}}月</text>
              <text class="weekday">{{todayInfo.solar.weekday}}</text>
            </view>
          </view>
          
          <view class="lunar-info">
            <view class="lunar-date">
              <text class="lunar-text">{{todayInfo.lunar.monthCn}}{{todayInfo.lunar.dayCn}}</text>
              <text class="ganzhi-year">{{todayInfo.lunar.gzYear}}年 [{{todayInfo.lunar.animal}}]</text>
            </view>
            
            <view class="calendar-details">
              <view class="term-info" wx:if="{{todayInfo.lunar.term}}">
                <text class="term-icon">🌞</text>
                <text class="term-text">{{todayInfo.lunar.term}}</text>
              </view>
              
              <view class="yiji-info">
                <view class="yi-items" wx:if="{{todayInfo.lunar.yiji.yi.length}}">
                  <text class="yiji-label">宜：</text>
                  <view class="yiji-content">
                    <view class="yiji-tag" wx:for="{{todayInfo.lunar.yiji.yi}}" wx:key="name" bindtap="showYiJiDetail" data-type="yi" data-item="{{item}}">
                      <text class="tag-text">{{item.name}}</text>
                    </view>
                  </view>
                </view>
                <view class="ji-items" wx:if="{{todayInfo.lunar.yiji.ji.length}}">
                  <text class="yiji-label">忌：</text>
                  <view class="yiji-content">
                    <view class="yiji-tag" wx:for="{{todayInfo.lunar.yiji.ji}}" wx:key="name" bindtap="showYiJiDetail" data-type="ji" data-item="{{item}}">
                      <text class="tag-text">{{item.name}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 节气提醒和签到区域 -->
      <view class="header-section" wx:if="{{solarTerms.isToday || !signInStatus}}">
        <view class="solar-terms-notice" wx:if="{{solarTerms.isToday}}">
          <image src="/assets/icons/solar-terms.png" class="notice-icon" mode="aspectFit" />
          <text class="notice-text">今日{{solarTerms.name}}，万物更新</text>
        </view>
        <view class="sign-in-section" wx:if="{{!signInStatus}}">
          <view class="sign-in-card" bindtap="handleSignIn">
            <view class="sign-in-icon">📅</view>
            <view class="sign-in-text">
              <text class="sign-title">今日签到</text>
              <text class="sign-desc">获取积分奖励</text>
            </view>
            <view class="sign-btn">签到</view>
          </view>
        </view>
      </view>

      <!-- 顶部轮播图 -->
        <block wx:if="{{banners && banners.length > 0}}">
          <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#ffffff">
            <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-url="{{item.url}}">
              <view class="banner-item">
          <image src="{{item.imageUrl}}" mode="aspectFill" class="banner-image" />
                <view class="banner-title">{{item.title}}</view>
              </view>
        </swiper-item>
      </swiper>
        </block>
      <view class="error-tip" wx:else>轮播图加载失败</view>

      <!-- 最近使用功能 -->
      <view class="section" wx:if="{{recentUsed.length > 0}}">
        <view class="section-title">
          <text>最近使用</text>
          <text class="section-more" bindtap="clearRecentUsed">清空</text>
        </view>
        <scroll-view class="recent-scroll" scroll-x>
          <view class="recent-item" wx:for="{{recentUsed}}" wx:key="type" bindtap="onRecentItemTap" data-type="{{item.type}}">
            <image src="{{item.icon}}" class="recent-icon" mode="aspectFit" />
            <text class="recent-text">{{item.name}}</text>
          </view>
        </scroll-view>
      </view>

        <!-- 测算类目 -->
        <view class="category-section">
          <view class="section-title">测算项目</view>
          <view class="category-grid">
            <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-id="{{item.id}}" data-url="{{item.url}}">
              <image class="category-icon" src="{{item.icon}}" mode="aspectFit" />
              <text class="category-name">{{item.name}}</text>
          </view>
        </view>
      </view>

        <!-- 热门测算 -->
        <view class="hot-section">
          <view class="section-title">
            <text>热门推荐</text>
            <text class="section-more" bindtap="goToMore">查看更多</text>
          </view>
          <view class="hot-list">
            <view class="hot-item" wx:for="{{hotItems}}" wx:key="id" bindtap="onHotItemTap" data-id="{{item.id}}" data-url="{{item.url}}">
              <image class="hot-image" src="{{item.imageUrl}}" mode="aspectFill" />
              <view class="hot-info">
                <text class="hot-title">{{item.title}}</text>
                <text class="hot-desc">{{item.description}}</text>
                <view class="hot-meta">
                  <text class="hot-price">￥{{item.price}}</text>
                  <text class="hot-count">{{item.orderCount}}人测算</text>
          </view>
              </view>
          </view>
        </view>
      </view>

        <!-- 热门文章 -->
        <view class="article-section">
          <view class="section-title">
            <text>命理知识</text>
            <text class="section-more" bindtap="goToArticles">查看更多</text>
          </view>
        <view class="article-list">
            <view class="article-item" wx:for="{{articles}}" wx:key="id" bindtap="onArticleTap" data-id="{{item.id}}">
              <image class="article-image" src="{{item.coverUrl}}" mode="aspectFill" />
            <view class="article-info">
              <text class="article-title">{{item.title}}</text>
              <view class="article-meta">
                <text class="article-author">{{item.author}}</text>
                  <text class="article-views">{{item.viewCount}}阅读</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>

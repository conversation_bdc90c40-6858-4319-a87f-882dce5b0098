// pages/profile/profile.js
const app = getApp()
const globalState = require('../../utils/global-state')
const navigationManager = require('../../utils/navigation')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    points: 0,
    level: 1,
    favorites: [],
    records: [],
    settings: {
      theme: 'light',
      notification: true
    },
    activeTab: 'records',
    birthInfo: null,
    signInStatus: false,
    isWxWork: false,
    menuItems: [
      {
        id: 'birth-info',
        title: '出生信息',
        icon: '/assets/icons/profile/birth-info.png',
        url: '/pages/birth-info/birth-info',
        desc: '管理您的出生信息'
      },
      {
        id: 'sign-in',
        title: '签到中心',
        icon: '/assets/icons/profile/sign-in.png',
        url: '/pages/sign-in/sign-in',
        desc: '每日签到获取积分'
      },
      {
        id: 'recharge',
        title: '充值中心',
        icon: '/assets/icons/profile/recharge.png',
        url: '/pages/recharge/recharge',
        desc: '充值获取更多服务'
      },
      {
        id: 'feedback',
        title: '意见反馈',
        icon: '/assets/icons/profile/feedback.png',
        url: '/pages/feedback/feedback',
        desc: '帮助我们改进产品'
      },
      {
        id: 'settings',
        title: '设置',
        icon: '/assets/icons/profile/settings.png',
        url: '/pages/settings/settings',
        desc: '个性化设置'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 初始化全局状态监听
    this.initStateListeners()

    // 加载初始数据
    this.loadInitialData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        // 调用自定义 tabBar 的 setSelected 方法
        this.getTabBar().setSelected()
      }
    } catch (error) {
      console.error('设置 TabBar 选中状态失败:', error)
    }

    // 刷新全局状态数据
    this.refreshGlobalState()
    this.loadUserData()
  },

  /**
   * 初始化状态监听器
   */
  initStateListeners() {
    // 监听用户信息变化
    this.userInfoListener = globalState.addListener('userInfo', (newUserInfo) => {
      this.setData({
        userInfo: newUserInfo,
        hasUserInfo: !!newUserInfo
      })
    })

    // 监听积分变化
    this.pointsListener = globalState.addListener('points', (newPoints) => {
      this.setData({
        points: newPoints,
        level: this.calculateLevel(newPoints)
      })
    })

    // 监听出生信息变化
    this.birthInfoListener = globalState.addListener('birthInfo', (newBirthInfo) => {
      this.setData({ birthInfo: newBirthInfo })
    })

    // 监听签到状态变化
    this.signInListener = globalState.addListener('signInStatus', (newStatus) => {
      this.setData({ signInStatus: newStatus })
    })
  },

  /**
   * 加载初始数据
   */
  loadInitialData() {
    const state = globalState.getState()
    this.setData({
      userInfo: state.userInfo,
      hasUserInfo: !!state.userInfo,
      points: state.points,
      level: this.calculateLevel(state.points),
      birthInfo: state.birthInfo,
      signInStatus: state.signInStatus,
      isWxWork: state.isWxWork
    })
  },

  /**
   * 刷新全局状态
   */
  refreshGlobalState() {
    globalState.loadFromStorage()
    this.loadInitialData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.userInfo.userId) {
      this.loadUserStats()
    }
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '玄学社区',
      path: '/pages/profile/profile'
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    }
  },

  // 登录
  login() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = {
          ...res.userInfo,
          userId: 'user_' + Date.now().toString().slice(-6),
          loginTime: new Date().toLocaleString()
        }
        
        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', userInfo)
        
        // 更新页面数据
        this.setData({ userInfo })
        
        // 加载用户统计数据
        this.loadUserStats()
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('登录失败:', err)
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 加载用户统计数据
  loadUserStats() {
    // 模拟数据，实际项目中应该从服务器获取
    const mockStats = {
      posts: 12,
      followers: 128,
      following: 56
    }
    
    this.setData({
      userStats: mockStats
    })
  },

  // 页面导航
  navigateTo(e) {
    const url = e.currentTarget.dataset.url
    const requireLogin = e.currentTarget.dataset.requireLogin

    // 检查是否需要登录
    if (requireLogin && !globalState.isUserLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '该功能需要登录后才能使用，是否前往登录？',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.login()
          }
        }
      })
      return
    }

    // 使用统一导航管理器
    navigationManager.navigateTo(url)
  },

  // 菜单项点击
  onMenuItemTap(e) {
    const { url, id } = e.currentTarget.dataset

    // 特殊处理某些菜单项
    switch (id) {
      case 'sign-in':
        // 如果已签到，显示签到状态
        if (this.data.signInStatus) {
          wx.showToast({
            title: '今日已签到',
            icon: 'success'
          })
          return
        }
        break
      case 'birth-info':
        // 出生信息页面不需要特殊检查
        break
    }

    navigationManager.navigateTo(url)
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用全局状态管理器退出登录
          globalState.logout()

          // 重置页面数据
          this.setData({
            userInfo: null,
            hasUserInfo: false,
            points: 0,
            level: 1,
            userStats: {
              posts: 0,
              followers: 0,
              following: 0
            }
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })

          // 返回首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }, 1500)
        }
      }
    })
  },

  /**
   * 页面卸载时清理监听器
   */
  onUnload() {
    // 清理状态监听器
    if (this.userInfoListener) this.userInfoListener()
    if (this.pointsListener) this.pointsListener()
    if (this.birthInfoListener) this.birthInfoListener()
    if (this.signInListener) this.signInListener()
  },

  async loadUserData() {
    if (!app.globalData.openid) {
      return
    }

    try {
      const db = wx.cloud.database()
      
      // 获取用户信息
      const userRes = await db.collection('users').where({
        _openid: app.globalData.openid
      }).get()
      
      if (userRes.data.length > 0) {
        const userData = userRes.data[0]
        this.setData({
          userInfo: userData,
          hasUserInfo: true,
          points: userData.points || 0,
          level: this.calculateLevel(userData.points || 0)
        })
      }
      
      // 获取收藏列表
      const favRes = await db.collection('user_favorites')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get()
      
      // 获取收藏的帖子详情
      const postIds = favRes.data.map(item => item.postId)
      const posts = await db.collection('posts')
        .where({
          _id: db.command.in(postIds)
        })
        .get()
      
      this.setData({
        favorites: posts.data.map(post => ({
          id: post._id,
          title: post.title,
          createTime: this.formatTime(post.createTime)
        }))
      })
      
      // 获取测算记录
      const recordRes = await db.collection('divination_records')
        .where({
          _openid: app.globalData.openid
        })
        .orderBy('createTime', 'desc')
        .get()
      
      this.setData({
        records: recordRes.data.map(record => ({
          id: record._id,
          type: record.type,
          result: record.result,
          createTime: this.formatTime(record.createTime)
        }))
      })
    } catch (error) {
      console.error('加载用户数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  calculateLevel(points) {
    // 等级计算规则：每100点升一级
    return Math.floor(points / 100) + 1
  },

  formatTime(timestamp) {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  },

  getUserProfile(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
        this.updateUserInfo(res.userInfo)
      }
    })
  },

  async updateUserInfo(userInfo) {
    try {
      const db = wx.cloud.database()
      await db.collection('users').where({
        _openid: app.globalData.openid
      }).update({
        data: {
          ...userInfo,
          updateTime: new Date()
        }
      })
    } catch (error) {
      console.error('更新用户信息失败:', error)
    }
  },

  switchTab(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  toggleTheme() {
    const newTheme = this.data.settings.theme === 'light' ? 'dark' : 'light'
    this.setData({
      'settings.theme': newTheme
    })
    wx.setStorageSync('theme', newTheme)
  },

  toggleNotification() {
    this.setData({
      'settings.notification': !this.data.settings.notification
    })
    wx.setStorageSync('notification', this.data.settings.notification)
  },

  // 查看收藏的帖子
  viewFavorite(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${id}`
    })
  },

  // 查看测算记录详情
  viewRecord(e) {
    const { id, type } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/${type}/detail?id=${id}`
    })
  },

  // 提交反馈
  submitFeedback(e) {
    const { content } = e.detail.value
    if (!content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }

    const db = wx.cloud.database()
    db.collection('feedback').add({
      data: {
        content: content.trim(),
        createTime: new Date()
      }
    }).then(() => {
      wx.showToast({
        title: '反馈成功',
        icon: 'success'
      })
    }).catch(error => {
      console.error('提交反馈失败:', error)
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    })
  }
})
/**
 * 配置切换脚本
 * 用于在普通微信小程序和企业微信小程序配置之间切换
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const APP_JSON_PATH = path.join(__dirname, '../app.json');
const PROJECT_CONFIG_PATH = path.join(__dirname, '../project.config.json');
const WXWORK_CONFIG_PATH = path.join(__dirname, '../wxwork.config.json');

// 备份文件路径
const APP_JSON_BACKUP = path.join(__dirname, '../app.json.backup');
const PROJECT_CONFIG_BACKUP = path.join(__dirname, '../project.config.json.backup');

/**
 * 读取JSON文件
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error);
    return null;
  }
}

/**
 * 写入JSON文件
 */
function writeJsonFile(filePath, data) {
  try {
    const content = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 文件已更新: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`写入文件失败: ${filePath}`, error);
    return false;
  }
}

/**
 * 创建备份
 */
function createBackup() {
  try {
    if (fs.existsSync(APP_JSON_PATH)) {
      fs.copyFileSync(APP_JSON_PATH, APP_JSON_BACKUP);
    }
    if (fs.existsSync(PROJECT_CONFIG_PATH)) {
      fs.copyFileSync(PROJECT_CONFIG_PATH, PROJECT_CONFIG_BACKUP);
    }
    console.log('✅ 配置文件备份完成');
    return true;
  } catch (error) {
    console.error('❌ 备份失败:', error);
    return false;
  }
}

/**
 * 恢复备份
 */
function restoreBackup() {
  try {
    if (fs.existsSync(APP_JSON_BACKUP)) {
      fs.copyFileSync(APP_JSON_BACKUP, APP_JSON_PATH);
    }
    if (fs.existsSync(PROJECT_CONFIG_BACKUP)) {
      fs.copyFileSync(PROJECT_CONFIG_BACKUP, PROJECT_CONFIG_PATH);
    }
    console.log('✅ 配置文件恢复完成');
    return true;
  } catch (error) {
    console.error('❌ 恢复失败:', error);
    return false;
  }
}

/**
 * 切换到企业微信配置
 */
function switchToWxwork() {
  console.log('🔄 正在切换到企业微信配置...');
  
  // 创建备份
  if (!createBackup()) {
    return false;
  }
  
  // 读取配置文件
  const appJson = readJsonFile(APP_JSON_PATH);
  const projectConfig = readJsonFile(PROJECT_CONFIG_PATH);
  const wxworkConfig = readJsonFile(WXWORK_CONFIG_PATH);
  
  if (!appJson || !projectConfig || !wxworkConfig) {
    console.error('❌ 读取配置文件失败');
    return false;
  }
  
  // 合并企业微信配置到 app.json
  const updatedAppJson = {
    ...appJson,
    ...wxworkConfig.app_json_wxwork_config
  };
  
  // 合并企业微信配置到 project.config.json
  const updatedProjectConfig = {
    ...projectConfig,
    ...wxworkConfig.project_config_wxwork
  };
  
  // 写入更新后的配置
  if (writeJsonFile(APP_JSON_PATH, updatedAppJson) && 
      writeJsonFile(PROJECT_CONFIG_PATH, updatedProjectConfig)) {
    console.log('✅ 已切换到企业微信配置');
    console.log('⚠️  请记得在 project.config.json 中填写正确的 corpid、agentid 和 secretKey');
    return true;
  }
  
  return false;
}

/**
 * 切换到普通微信小程序配置
 */
function switchToWechat() {
  console.log('🔄 正在切换到普通微信小程序配置...');
  
  if (restoreBackup()) {
    console.log('✅ 已切换到普通微信小程序配置');
    return true;
  }
  
  return false;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
📋 配置切换脚本使用说明

用法:
  node scripts/switch-config.js [command]

命令:
  wxwork    切换到企业微信配置
  wechat    切换到普通微信小程序配置
  backup    创建当前配置的备份
  restore   恢复备份的配置
  check     检查当前配置状态
  help      显示此帮助信息

示例:
  node scripts/switch-config.js wxwork   # 切换到企业微信
  node scripts/switch-config.js wechat   # 切换到普通微信
  node scripts/switch-config.js check    # 检查当前配置
  `);
}

// 主函数
function main() {
  const command = process.argv[2];

  switch (command) {
    case 'wxwork':
      switchToWxwork();
      break;
    case 'wechat':
      switchToWechat();
      break;
    case 'backup':
      createBackup();
      break;
    case 'restore':
      restoreBackup();
      break;
    case 'check':
      checkConfig();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      console.log('❌ 未知命令，使用 --help 查看帮助');
      showHelp();
  }
}

/**
 * 检查当前配置状态
 */
function checkConfig() {
  console.log('🔍 检查当前配置状态...\n');

  const appJson = readJsonFile(APP_JSON_PATH);
  const projectConfig = readJsonFile(PROJECT_CONFIG_PATH);

  if (!appJson || !projectConfig) {
    console.error('❌ 无法读取配置文件');
    return false;
  }

  // 检查是否为企业微信配置
  const isWxworkConfig = appJson.wxwork && appJson.wxwork.enable;

  console.log(`📱 当前配置类型: ${isWxworkConfig ? '企业微信小程序' : '普通微信小程序'}`);
  console.log(`📦 项目名称: ${projectConfig.projectname}`);
  console.log(`🆔 AppID: ${projectConfig.appid}`);

  if (appJson.plugins && appJson.plugins.WechatSI) {
    console.log(`🔌 微信同声传译插件版本: ${appJson.plugins.WechatSI.version}`);
  }

  if (isWxworkConfig) {
    console.log('\n🏢 企业微信配置:');
    console.log(`   - 通讯录权限: ${appJson.wxwork.permissions.readContacts ? '✅' : '❌'}`);
    console.log(`   - 部门权限: ${appJson.wxwork.permissions.readDepartments ? '✅' : '❌'}`);
    console.log(`   - 消息权限: ${appJson.wxwork.permissions.sendMessages ? '✅' : '❌'}`);

    if (projectConfig.wxwork) {
      console.log(`   - 企业ID: ${projectConfig.wxwork.corpid || '未配置'}`);
      console.log(`   - 应用ID: ${projectConfig.wxwork.agentid || '未配置'}`);
      console.log(`   - 密钥: ${projectConfig.wxwork.secretKey ? '已配置' : '未配置'}`);
    }
  }

  // 检查备份文件
  const hasBackup = fs.existsSync(APP_JSON_BACKUP) && fs.existsSync(PROJECT_CONFIG_BACKUP);
  console.log(`\n💾 配置备份: ${hasBackup ? '存在' : '不存在'}`);

  console.log('\n✅ 配置检查完成');
  return true;
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  switchToWxwork,
  switchToWechat,
  createBackup,
  restoreBackup,
  checkConfig
};

{"description": "企业微信配置文件 - 在企业微信开发者工具中使用时，将此配置合并到 app.json 和 project.config.json", "app_json_wxwork_config": {"wxwork": {"enable": true, "permissions": {"readContacts": true, "readDepartments": true, "sendMessages": true, "createMeeting": false, "readCalendar": false, "writeCalendar": false}, "allowedDomains": ["*.your-company.com", "*.trusted-partner.com"]}}, "project_config_wxwork": {"wxwork": {"enable": true, "corpid": "", "agentid": "", "secretKey": ""}}, "usage_instructions": {"1": "在普通微信小程序开发时，使用当前的 app.json 和 project.config.json", "2": "在企业微信开发时，将 app_json_wxwork_config 中的配置添加到 app.json", "3": "在企业微信开发时，将 project_config_wxwork 中的配置添加到 project.config.json", "4": "记得填写正确的 corpid、agentid 和 secretKey"}}